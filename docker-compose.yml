version: '3.8'

services:
  # Base de données PostgreSQL
  postgres:
    image: postgres:15-alpine
    container_name: ecotask_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ecotask
      POSTGRES_USER: ecotask_user
      POSTGRES_PASSWORD: ecotask_password
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db:/docker-entrypoint-initdb.d
    networks:
      - ecotask_network

  # Serveur Express (API)
  server:
    build:
      context: .
      dockerfile: Dockerfile.server
    container_name: ecotask_server
    restart: unless-stopped
    environment:
      NODE_ENV: development
      DATABASE_URL: ********************************************************/ecotask
      PORT: 3001
      CORS_ORIGIN: http://localhost:3000
    ports:
      - "3001:3001"
    volumes:
      - ./src:/app/src
      - ./prisma:/app/prisma
      - ./package.json:/app/package.json
      - ./yarn.lock:/app/yarn.lock
      - ./tsconfig.json:/app/tsconfig.json
    depends_on:
      - postgres
    networks:
      - ecotask_network
    command: yarn dev

  # Client React (Frontend)
  client:
    build:
      context: ../client
      dockerfile: ../server/Dockerfile.client
    container_name: ecotask_client
    restart: unless-stopped
    environment:
      VITE_API_URL: http://localhost:3001
    ports:
      - "3000:3000"
    volumes:
      - ../client/src:/app/src
      - ../client/public:/app/public
      - ../client/package.json:/app/package.json
      - ../client/yarn.lock:/app/yarn.lock
      - ../client/vite.config.ts:/app/vite.config.ts
      - ../client/tailwind.config.js:/app/tailwind.config.js
      - ../client/postcss.config.js:/app/postcss.config.js
      - ../client/tsconfig.json:/app/tsconfig.json
      - ../client/tsconfig.app.json:/app/tsconfig.app.json
      - ../client/tsconfig.node.json:/app/tsconfig.node.json
      - ../client/index.html:/app/index.html
    networks:
      - ecotask_network
    command: yarn start

volumes:
  postgres_data:

networks:
  ecotask_network:
    driver: bridge
