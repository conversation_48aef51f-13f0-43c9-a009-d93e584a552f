# Dockerfile pour le serveur Express
FROM node:18-alpine

# Définir le répertoire de travail
WORKDIR /app

# Installer les dépendances système nécessaires
RUN apk add --no-cache openssl netcat-openbsd

# Activer Corepack pour Yarn 4
RUN corepack enable

# Copier les fichiers de configuration des dépendances
COPY package.json yarn.lock ./

# Installer les dépendances
RUN yarn install --frozen-lockfile

# Copier le code source
COPY . .

# Exposer le port
EXPOSE 3001

# Commande par défaut (sera surchargée par docker-compose en dev)
CMD ["yarn", "dev"]
