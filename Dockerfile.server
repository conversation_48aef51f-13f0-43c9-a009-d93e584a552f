# Dockerfile pour le serveur Express
FROM node:18-alpine

# Définir le répertoire de travail
WORKDIR /app

# Installer les dépendances système nécessaires
RUN apk add --no-cache openssl

# Copier les fichiers de configuration des dépendances
COPY package.json yarn.lock ./

# Installer les dépendances
RUN yarn install --frozen-lockfile

# Copier le code source
COPY . .

# Générer le client Prisma
RUN npx prisma generate

# Exposer le port
EXPOSE 3001

# Commande par défaut (sera surchargée par docker-compose en dev)
CMD ["yarn", "start"]
