{"name": "server", "packageManager": "yarn@4.4.1", "dependencies": {"@prisma/client": "^6.9.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0"}, "scripts": {"tsc:init": "tsc --init --rootDir src --outDir dist --esModuleInterop --resolveJsonModule --lib ES2021 --module commonjs", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "start": "node dist/index.js"}, "devDependencies": {"@types/express": "^5.0.3", "@types/node": "^24.0.0", "prisma": "^6.9.0", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}