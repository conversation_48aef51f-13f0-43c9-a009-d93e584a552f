# Dockerfile pour le client React
FROM node:18-alpine

# Définir le répertoire de travail
WORKDIR /app

# Activer Corepack pour Yarn 4
RUN corepack enable

# Copier les fichiers de configuration des dépendances
COPY package.json yarn.lock ./

# Installer les dépendances
RUN yarn install --frozen-lockfile

# Copier le code source
COPY . .

# Exposer le port
EXPOSE 3000

# Commande par défaut (sera surchargée par docker-compose en dev)
CMD ["yarn", "start"]
