#!/bin/bash

echo "🚀 Démarrage de l'application EcoTask en mode développement"

# Couleurs pour les messages
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Vérifier si Docker est installé
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker n'est pas installé. Veuillez l'installer d'abord.${NC}"
    exit 1
fi

# Vérifier si Docker Compose est disponible
if ! docker compose version &> /dev/null; then
    echo -e "${RED}❌ Docker Compose n'est pas disponible. Veuillez l'installer d'abord.${NC}"
    exit 1
fi

echo -e "${BLUE}🐳 Démarrage des conteneurs Docker...${NC}"

# Démarrer PostgreSQL en premier
echo -e "${BLUE}📦 Démarrage de PostgreSQL...${NC}"
docker compose up -d postgres

# Attendre que PostgreSQL soit prêt
echo -e "${BLUE}⏳ Attente de PostgreSQL...${NC}"
sleep 10

# Construire et démarrer le serveur
echo -e "${BLUE}🔧 Construction et démarrage du serveur...${NC}"
docker compose up -d server

# Construire et démarrer le client
echo -e "${BLUE}🎨 Construction et démarrage du client...${NC}"
docker compose up -d client

echo -e "${GREEN}✅ Application démarrée !${NC}"
echo ""
echo -e "${YELLOW}📱 URLs de l'application :${NC}"
echo -e "${BLUE}  Frontend: http://localhost:3001${NC}"
echo -e "${BLUE}  API:      http://localhost:3002${NC}"
echo -e "${BLUE}  Database: localhost:5433${NC}"
echo ""
echo -e "${YELLOW}📋 Commandes utiles :${NC}"
echo -e "${BLUE}  docker compose logs -f        # Voir tous les logs${NC}"
echo -e "${BLUE}  docker compose logs server    # Logs du serveur${NC}"
echo -e "${BLUE}  docker compose logs client    # Logs du client${NC}"
echo -e "${BLUE}  docker compose down           # Arrêter l'application${NC}"
echo ""
echo -e "${YELLOW}🔧 Pour initialiser la base de données :${NC}"
echo -e "${BLUE}  docker compose exec server yarn db:migrate${NC}"
echo -e "${BLUE}  docker compose exec server yarn db:seed${NC}"
