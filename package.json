{"name": "server", "version": "1.0.0", "description": "EcoTask API Server", "main": "dist/index.js", "packageManager": "yarn@4.4.1", "scripts": {"tsc:init": "tsc --init --rootDir src --outDir dist --esModuleInterop --resolveJsonModule --lib ES2021 --module commonjs", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "start": "node dist/index.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:migrate:deploy": "prisma migrate deploy", "db:seed": "ts-node prisma/seed.ts", "db:studio": "prisma studio", "db:reset": "prisma migrate reset", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:build": "docker-compose build", "docker:logs": "docker-compose logs -f"}, "dependencies": {"@prisma/client": "6.9.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^5.0.3", "@types/node": "^24.0.0", "prisma": "6.9.0", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}