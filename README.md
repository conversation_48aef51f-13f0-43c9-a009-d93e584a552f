# EcoTask 🌱

Application web complète pour la gestion de tâches écologiques, développée avec React, Express, PostgreSQL et Docker.

## 🏗️ Architecture

- **Frontend**: React 19 + TypeScript + Vite + Tailwind CSS
- **Backend**: Express + TypeScript + Prisma ORM
- **Base de données**: PostgreSQL 15
- **Containerisation**: Docker + Docker Compose

## 🚀 Démarrage rapide

### Prérequis
- Docker et Docker Compose
- Node.js 18+ (pour le développement local)
- Yarn

### Installation et démarrage

1. **Configuration initiale**
   ```bash
   ./setup.sh
   ```

2. **Démarrer l'application**
   ```bash
   docker-compose up -d
   ```

3. **Initialiser la base de données**
   ```bash
   yarn db:migrate
   yarn db:seed
   ```

### URLs de l'application
- **Frontend**: http://localhost:3000
- **API**: http://localhost:3001
- **Base de données**: localhost:5432

## 📁 Structure du projet

```
ecoTask/
├── server/                 # API Express
│   ├── src/
│   │   ├── index.ts       # Point d'entrée
│   │   └── routes/        # Routes API
│   ├── prisma/
│   │   ├── schema.prisma  # Schéma de base de données
│   │   └── seed.ts        # Données de test
│   ├── docker-compose.yml
│   └── Dockerfile.server
└── client/                # Application React
    ├── src/
    ├── Dockerfile.client
    └── package.json
```

## 🗄️ Base de données

### Modèles Prisma

- **User**: Utilisateurs de l'application
- **Task**: Tâches écologiques avec catégories et points

### Catégories de tâches
- `ENERGY`: Économie d'énergie
- `WATER`: Économie d'eau
- `WASTE`: Gestion des déchets
- `TRANSPORT`: Transport écologique
- `FOOD`: Alimentation durable
- `NATURE`: Protection de la nature
- `OTHER`: Autres

## 🔧 Commandes utiles

### Développement
```bash
# Démarrer en mode développement
yarn dev                    # Serveur uniquement
docker-compose up          # Application complète

# Logs
docker-compose logs -f     # Tous les services
docker-compose logs server # Serveur uniquement
docker-compose logs client # Client uniquement
```

### Base de données
```bash
yarn db:generate          # Générer le client Prisma
yarn db:migrate           # Créer une migration
yarn db:push              # Pousser le schéma vers la DB
yarn db:seed              # Peupler avec des données de test
yarn db:studio            # Ouvrir Prisma Studio
yarn db:reset             # Reset complet de la DB
```

### Docker
```bash
yarn docker:up            # Démarrer les conteneurs
yarn docker:down          # Arrêter les conteneurs
yarn docker:build         # Reconstruire les images
yarn docker:logs          # Voir les logs
```

## 🌐 API Endpoints

### Utilisateurs
- `GET /api/users` - Liste des utilisateurs
- `GET /api/users/:id` - Détails d'un utilisateur
- `POST /api/users` - Créer un utilisateur
- `PUT /api/users/:id` - Modifier un utilisateur
- `DELETE /api/users/:id` - Supprimer un utilisateur

### Tâches
- `GET /api/tasks` - Liste des tâches (avec filtres)
- `GET /api/tasks/:id` - Détails d'une tâche
- `POST /api/tasks` - Créer une tâche
- `PUT /api/tasks/:id` - Modifier une tâche
- `DELETE /api/tasks/:id` - Supprimer une tâche
- `PATCH /api/tasks/:id/complete` - Marquer comme complétée

### Santé
- `GET /` - Informations de l'API
- `GET /health` - État de santé (API + DB)

## 🎨 Frontend

Le frontend utilise Tailwind CSS avec une palette de couleurs écologiques personnalisée (`eco-green`).

### Composants principaux
- Interface de gestion des tâches
- Système de points et gamification
- Filtres par catégorie
- Tableau de bord utilisateur

## 🔒 Variables d'environnement

### Serveur (.env)
```env
DATABASE_URL=postgresql://ecotask_user:ecotask_password@localhost:5432/ecotask
NODE_ENV=development
PORT=3001
CORS_ORIGIN=http://localhost:3000
```

### Client
```env
VITE_API_URL=http://localhost:3001
```

## 🐳 Docker

L'application est entièrement containerisée avec :
- **postgres**: Base de données PostgreSQL
- **server**: API Express
- **client**: Application React

Tous les services communiquent via un réseau Docker dédié (`ecotask_network`).

## 📝 Développement

### Ajout de nouvelles fonctionnalités
1. Modifier le schéma Prisma si nécessaire
2. Créer les migrations : `yarn db:migrate`
3. Ajouter les routes API dans `src/routes/`
4. Implémenter les composants React
5. Tester avec `yarn dev`

### Tests
```bash
# TODO: Ajouter les tests unitaires et d'intégration
```

## 🤝 Contribution

1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/AmazingFeature`)
3. Commit les changements (`git commit -m 'Add some AmazingFeature'`)
4. Push vers la branche (`git push origin feature/AmazingFeature`)
5. Ouvrir une Pull Request

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.
