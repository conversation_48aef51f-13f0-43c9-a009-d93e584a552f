#!/bin/bash
set -e

echo "🚀 Initialisation du serveur EcoTask..."

# Attendre que PostgreSQL soit prêt
echo "⏳ Attente de PostgreSQL..."
while ! nc -z postgres 5432; do
  sleep 1
done
echo "✅ PostgreSQL est prêt !"

# Générer le client Prisma
echo "🔧 Génération du client Prisma..."
yarn db:generate || echo "⚠️ Génération Prisma échouée, continuons..."

# Exécuter les migrations si nécessaire
echo "📊 Exécution des migrations..."
yarn db:push || echo "⚠️ Migrations échouées, continuons..."

# Démarrer l'application
echo "🎯 Démarrage de l'application..."
exec "$@"
