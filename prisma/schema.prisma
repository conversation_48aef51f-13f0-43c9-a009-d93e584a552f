// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Modèle User pour les utilisateurs
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  tasks Task[]

  @@map("users")
}

// Modèle Task pour les tâches écologiques
model Task {
  id          String     @id @default(cuid())
  title       String
  description String?
  category    TaskCategory
  points      Int        @default(0)
  completed   Boolean    @default(false)
  dueDate     DateTime?
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  // Relations
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("tasks")
}

// Énumération pour les catégories de tâches
enum TaskCategory {
  ENERGY      // Économie d'énergie
  WATER       // Économie d'eau
  WASTE       // Gestion des déchets
  TRANSPORT   // Transport écologique
  FOOD        // Alimentation durable
  NATURE      // Protection de la nature
  OTHER       // Autres
}
