import { PrismaClient, TaskCategory } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Créer des utilisateurs de test
  const user1 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: '<PERSON>',
    },
  });

  const user2 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: '<PERSON>',
    },
  });

  console.log('✅ Users created:', { user1, user2 });

  // Créer des tâches de test
  const tasks = [
    {
      title: 'Éteindre les lumières en sortant',
      description: 'Penser à éteindre toutes les lumières avant de quitter une pièce',
      category: TaskCategory.ENERGY,
      points: 5,
      userId: user1.id,
    },
    {
      title: 'Prendre une douche de 5 minutes maximum',
      description: 'Limiter le temps de douche pour économiser l\'eau',
      category: TaskCategory.WATER,
      points: 10,
      userId: user1.id,
    },
    {
      title: 'Trier les déchets recyclables',
      description: 'Séparer le plastique, le verre et le papier',
      category: TaskCategory.WASTE,
      points: 8,
      userId: user2.id,
    },
    {
      title: 'Utiliser les transports en commun',
      description: 'Prendre le bus ou le métro au lieu de la voiture',
      category: TaskCategory.TRANSPORT,
      points: 15,
      userId: user2.id,
    },
    {
      title: 'Acheter des produits locaux',
      description: 'Privilégier les produits de saison et locaux',
      category: TaskCategory.FOOD,
      points: 12,
      userId: user1.id,
    },
    {
      title: 'Planter une graine',
      description: 'Planter une graine dans le jardin ou en pot',
      category: TaskCategory.NATURE,
      points: 20,
      userId: user2.id,
      completed: true,
    },
  ];

  for (const taskData of tasks) {
    const task = await prisma.task.upsert({
      where: { 
        id: `seed-${taskData.title.toLowerCase().replace(/\s+/g, '-')}` 
      },
      update: {},
      create: {
        id: `seed-${taskData.title.toLowerCase().replace(/\s+/g, '-')}`,
        ...taskData,
      },
    });
    console.log(`✅ Task created: ${task.title}`);
  }

  console.log('🎉 Database seeding completed!');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
