#!/bin/bash

echo "🚀 Configuration de l'application EcoTask"

# Couleurs pour les messages
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${BLUE}📦 Installation des dépendances du serveur...${NC}"
yarn install

echo -e "${BLUE}📦 Installation des dépendances du client...${NC}"
cd ../client
yarn add -D tailwindcss postcss autoprefixer
yarn install
cd ../server

echo -e "${BLUE}🗄️ Génération du client Prisma...${NC}"
yarn db:generate

echo -e "${BLUE}🐳 Construction des images Docker...${NC}"
docker-compose build

echo -e "${GREEN}✅ Configuration terminée !${NC}"
echo ""
echo -e "${YELLOW}Pour démarrer l'application :${NC}"
echo -e "${BLUE}  docker-compose up -d${NC}    # Démarrer en arrière-plan"
echo -e "${BLUE}  docker-compose up${NC}       # Démarrer avec logs"
echo ""
echo -e "${YELLOW}Pour gérer la base de données :${NC}"
echo -e "${BLUE}  yarn db:migrate${NC}         # Créer les migrations"
echo -e "${BLUE}  yarn db:seed${NC}            # Peupler avec des données de test"
echo -e "${BLUE}  yarn db:studio${NC}          # Ouvrir Prisma Studio"
echo ""
echo -e "${YELLOW}URLs de l'application :${NC}"
echo -e "${BLUE}  Frontend: http://localhost:3000${NC}"
echo -e "${BLUE}  API:      http://localhost:3001${NC}"
echo -e "${BLUE}  Database: localhost:5432${NC}"
